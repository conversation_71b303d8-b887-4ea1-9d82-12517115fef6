# SSE Broadcast System with React and IndexedDB

## Project Overview

A complete Server-Sent Events (SSE) broadcast system implementation with React/TypeScript frontend and NestJS backend. The system manages real-time communication across multiple browser tabs using a master-slave architecture, BroadcastChannel API, and IndexedDB for persistent storage.

## Architecture

### System Components
- **Frontend**: React 18 + TypeScript + Vite
- **Backend**: NestJS + Express
- **Storage**: IndexedDB (client-side)
- **Communication**: SSE + BroadcastChannel API

### Project Structure
```
SSE-cleaned/
├── backend-app/                 # NestJS Backend
│   ├── src/
│   │   ├── auth/               # Authentication module
│   │   ├── sse/                # SSE controller & service
│   │   └── main.ts             # Application entry point
│   └── package.json
├── src/                        # React Frontend
│   ├── components/             # React components
│   │   ├── App.tsx            # Main application
│   │   ├── ControlPanel.tsx   # Message generation UI
│   │   ├── NotificationsUI.tsx # Message display
│   │   ├── MasterCheck.tsx    # Master tab logic
│   │   └── BroadcastListener.tsx # Cross-tab communication
│   ├── services/              # Business logic
│   │   ├── SSEClient.ts       # SSE connection management
│   │   └── StorageService.ts  # IndexedDB operations
│   └── types/                 # TypeScript definitions
├── index.html                 # Vite entry point
├── package.json              # Frontend dependencies
└── vite.config.ts            # Build configuration
```

## Architecture Diagram

```mermaid
graph TD
    A[NestJS Backend :3000] --> B[Master Tab - SSE Connection]
    B --> C[BroadcastChannel 'notifications']
    C --> D[Slave Tab 1]
    C --> E[Slave Tab 2]
    C --> F[Slave Tab N]
    B --> G[IndexedDB]
    D --> G
    E --> G
    F --> G
    H[User Action] --> I[Any Tab]
    I --> J[Generate Message]
    J --> G
    J --> C
```

---

## Backend Implementation (NestJS)

### 1. Authentication Module (`src/auth/`)
- **JWT Token Verification**: Extracts and validates Bearer tokens
- **Mock Implementation**: Uses default credentials for development
- **User Context**: Provides `{ userId, tenantId, role }` from token

### 2. SSE Module (`src/sse/`)

#### SSE Controller (`sse.controller.ts`)
- **GET /events**: SSE endpoint with proper headers
- **POST /send**: Message broadcasting endpoint
- **Authentication**: Token extraction and validation
- **CORS**: Configured for frontend origins

#### SSE Service (`sse.service.ts`)
- **Connection Management**:
  ```typescript
  Map<tenantId, Map<userId, SseClient>>
  ```
- **Message Broadcasting**:
  - `sendEventToTenant()`: Broadcast to all users in tenant
  - `sendEventToUser()`: Send to specific user
  - `sendEventToRole()`: Send to users with specific role
- **Heartbeat**: 30-second intervals to maintain connections
- **Cleanup**: Automatic client removal on disconnect

### 3. API Endpoints

#### GET /events
- **Headers**: SSE-compliant headers
- **Authentication**: Bearer token support
- **Response**: Event stream with heartbeat

#### POST /send
- **Payload**:
```json
{
  "tenantId": "tenant_123",
  "target": "user" | "role" | "tenant",
  "targetId": "alice" | "admin" | null,
  "message": "Some update!"
}
```
- **Response**: `{ success: boolean }`

---

## Frontend Implementation (React + TypeScript)

### 1. Core Components

#### App.tsx
- **Main Application**: Root component with auth state management
- **Tab Status**: Displays master/slave tab status
- **Message Coordination**: Handles cross-component communication

#### MasterCheck.tsx
- **Master Tab Logic**: Determines if current tab should be master
- **SSE Connection**: Initiates connection for master tabs only
- **Lifecycle Management**: Handles connection cleanup

#### ControlPanel.tsx
- **Message Generation**: Creates test messages locally
- **Backend Communication**: Sends messages via POST /send
- **Cross-Tab Broadcasting**: Broadcasts local messages to other tabs

#### NotificationsUI.tsx
- **Message Display**: Shows messages from IndexedDB
- **Real-time Updates**: Refreshes when new messages arrive
- **User Interactions**: Mark as read, clear all functionality

#### BroadcastListener.tsx
- **Cross-Tab Communication**: Listens for messages from other tabs
- **Non-Visual Component**: Handles background message synchronization

### 2. Services Layer

#### SSEClient.ts (Singleton)
- **Connection Management**: Handles SSE connection lifecycle
- **Master Tab Detection**: Unique tab ID system for master identification
- **Message Processing**: Receives SSE messages and broadcasts to other tabs
- **Auto-Reconnection**: Handles connection failures with retry logic

#### StorageService.ts (Singleton)
- **IndexedDB Operations**: CRUD operations for message storage
- **Message Pruning**: Maintains maximum 100 messages
- **Schema Management**: Handles database initialization and upgrades

### 3. Data Flow Architecture

```mermaid
sequenceDiagram
    participant MT as Master Tab
    participant ST as Slave Tab
    participant BC as BroadcastChannel
    participant IDB as IndexedDB
    participant BE as Backend

    Note over MT,ST: Tab Initialization
    MT->>MT: Claim master role
    ST->>ST: Detect slave role
    MT->>BE: Connect to SSE

    Note over MT,ST: Message from Backend
    BE->>MT: SSE message
    MT->>IDB: Save message
    MT->>BC: Broadcast message
    BC->>ST: Deliver message
    ST->>IDB: Save message
    ST->>ST: Update UI

    Note over MT,ST: Local Message Generation
    ST->>IDB: Save message
    ST->>BC: Broadcast message
    BC->>MT: Deliver message
    MT->>MT: Update UI
```

### 4. Technical Implementation Details

#### Master Tab Detection
```typescript
// Unique tab identification
private tabId: string = Date.now().toString() + Math.random().toString(36).substring(2);

// Master role claiming
private async claimMasterRole(): Promise<boolean> {
  const currentMaster = localStorage.getItem('sse-master-tab');
  if (!currentMaster) {
    localStorage.setItem('sse-master-tab', this.tabId);
    return true;
  }
  return false;
}
```

#### IndexedDB Schema
```typescript
interface SSEMessage {
  id: string;           // UUID
  tenantId: string;     // Tenant identifier
  userId: string;       // User identifier
  role: string;         // User role
  timestamp: number;    // Message timestamp
  payload: {
    type: string;       // Message type
    content: string;    // Message content
    read?: boolean;     // Read status
    [key: string]: any; // Additional properties
  };
}
```

#### BroadcastChannel Communication
- **Channel Name**: `'notifications'`
- **Message Format**: Complete SSEMessage objects
- **Bidirectional**: Both master and slave tabs can broadcast
- **Automatic Cleanup**: Channels closed on tab unload

---

## Data Flow Summary

```mermaid
sequenceDiagram
    participant Server
    participant MasterTab
    participant OtherTab
    participant IndexedDB

    Server->>MasterTab: Push SSE message
    MasterTab->>BroadcastChannel: Post message
    BroadcastChannel->>OtherTab: Deliver message
    MasterTab->>IndexedDB: Save message
    OtherTab->>IndexedDB: Save message
    AllTabs->>UI: Show from IndexedDB
```

---

## Development Setup

### Prerequisites
- Node.js v20+ (managed via nvm)
- npm or yarn package manager

### Backend Setup
```bash
cd backend-app
npm install
npm run start:dev  # Runs on http://localhost:3000
```

### Frontend Setup
```bash
npm install
npm run dev        # Runs on http://localhost:5173
```

### Build Configuration
- **Frontend**: Vite + TypeScript + React
- **Backend**: NestJS + Express
- **Development**: Hot reload enabled for both frontend and backend

---

## Testing Cross-Tab Communication

### Test Scenarios
1. **Open Multiple Tabs**: Navigate to http://localhost:5173 in multiple browser tabs
2. **Master Tab Identification**: One tab shows "Master Tab (SSE Connected)", others show "Slave Tab (Listening to Broadcast)"
3. **Local Message Generation**: Click "Generate Test Message" in any tab - message appears in all tabs
4. **Backend Message Broadcasting**: Click "Send Message to Tenant" - message sent via SSE appears in all tabs
5. **Persistence**: Refresh tabs or open new ones - messages persist via IndexedDB
6. **Read Status Sync**: Mark messages as read - status syncs across tabs

### Key Features Verified
- ✅ Single SSE connection per browser session
- ✅ Real-time cross-tab message synchronization
- ✅ Persistent message storage with IndexedDB
- ✅ Automatic master/slave tab management
- ✅ Message broadcasting to all tenant users
- ✅ Graceful connection handling and reconnection

---

## Performance Optimizations

### Client-Side
- **Message Pruning**: Automatic cleanup of old messages (max 100)
- **Efficient Updates**: Component re-renders only when necessary
- **Memory Management**: Proper cleanup of event listeners and connections
- **Lazy Loading**: IndexedDB operations are asynchronous and non-blocking

### Server-Side
- **Connection Pooling**: Efficient management of SSE connections
- **Heartbeat Mechanism**: Maintains connection health
- **Automatic Cleanup**: Dead connections are automatically removed
- **Tenant Isolation**: Messages are properly scoped to tenants

---

## Deployment Considerations

### Backend Considerations
- **Reverse Proxy**: Configure nginx/Apache with increased timeout settings
- **Rate Limiting**: Implement throttling for `/send` endpoint
- **Redis Integration**: Optional for horizontal scaling across multiple instances
- **Monitoring**: Log SSE connections, disconnections, and error rates

### Frontend Considerations
- **Error Handling**: Graceful degradation for unsupported browsers
- **Performance Monitoring**: Track IndexedDB usage and memory consumption

This implementation provides a SSE broadcast system suitable for multi-tenant applications with cross-tab synchronization and persistent storage capabilities.
