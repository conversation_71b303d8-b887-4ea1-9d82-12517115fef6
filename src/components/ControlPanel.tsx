import React from 'react';
import { AuthState, SSEMessage } from '../types';
import StorageService from '../services/StorageService';

interface ControlPanelProps {
  auth: AuthState;
  onNewMessage: (message: SSEMessage) => void;
}

const ControlPanel: React.FC<ControlPanelProps> = ({ auth, onNewMessage }) => {
  const generateTestMessage = async () => {
    if (!auth.userId || !auth.tenantId || !auth.role) return;

    const message: Omit<SSEMessage, 'id'> = {
      tenantId: auth.tenantId,
      userId: auth.userId,
      role: auth.role,
      timestamp: Date.now(),
      payload: {
        type: 'message',
        content: `Test message at ${new Date().toLocaleTimeString()}`,
        read: false,
      },
    };

    const savedMessage = await StorageService.saveMessage(message);

    // Broadcast to other tabs
    try {
      const broadcastChannel = new BroadcastChannel('notifications');
      broadcastChannel.postMessage(savedMessage);
      broadcastChannel.close();
    } catch (error) {
      console.error('Error broadcasting message:', error);
    }

    onNewMessage(savedMessage);
  };

  const sendTenantMessage = async () => {
    if (!auth.token || !auth.tenantId) return;

    try {
      const response = await fetch('http://localhost:3000/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`,
        },
        body: JSON.stringify({
          tenantId: auth.tenantId,
          target: 'tenant',
          targetId: null,
          message: `Tenant message from ${auth.userId} at ${new Date().toLocaleTimeString()}`,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Message sent:', data);
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  return (
    <div className="control-panel">
      <h2>Control Panel</h2>
      <button onClick={generateTestMessage}>Generate Test Message</button>
      <div style={{ marginTop: '20px' }}>
        <button onClick={sendTenantMessage}>Send Message to Tenant</button>
      </div>
    </div>
  );
};

export default ControlPanel;
