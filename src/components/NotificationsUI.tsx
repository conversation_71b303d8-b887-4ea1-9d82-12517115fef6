import React, { useEffect, useState } from 'react';
import { SSEMessage } from '../types';
import StorageService from '../services/StorageService';

interface NotificationsUIProps {
  refreshTrigger?: number;
}

const NotificationsUI: React.FC<NotificationsUIProps> = ({ refreshTrigger }) => {
  const [messages, setMessages] = useState<SSEMessage[]>([]);

  const loadMessages = async () => {
    const storedMessages = await StorageService.getAllMessages();
    // Sort by timestamp (newest first)
    storedMessages.sort((a, b) => b.timestamp - a.timestamp);
    setMessages(storedMessages);
  };

  useEffect(() => {
    // Load messages from IndexedDB on mount
    loadMessages();

    // Set up a refresh interval as backup
    const refreshInterval = setInterval(loadMessages, 5000);

    return () => {
      clearInterval(refreshInterval);
    };
  }, []);

  // Refresh when refreshTrigger changes (from broadcast messages)
  useEffect(() => {
    if (refreshTrigger !== undefined) {
      loadMessages();
    }
  }, [refreshTrigger]);

  const handleMarkAsRead = async (id: string) => {
    await StorageService.markMessageAsRead(id);
    setMessages(prevMessages => 
      prevMessages.map(msg => 
        msg.id === id 
          ? { ...msg, payload: { ...msg.payload, read: true } } 
          : msg
      )
    );
  };

  const handleClearAll = async () => {
    await StorageService.clearAllMessages();
    setMessages([]);
  };

  return (
    <div className="notifications-container">
      <div className="notifications-header">
        <h2>Notifications</h2>
        <button onClick={handleClearAll}>Clear All</button>
      </div>
      
      {messages.length === 0 ? (
        <div className="no-notifications">No notifications</div>
      ) : (
        <ul className="notifications-list">
          {messages.map(message => (
            <li 
              key={message.id} 
              className={`notification-item ${message.payload.read ? 'read' : 'unread'}`}
            >
              <div className="notification-content">
                <div className="notification-message">{message.payload.content}</div>
                <div className="notification-timestamp">
                  {new Date(message.timestamp).toLocaleString()}
                </div>
              </div>
              {!message.payload.read && (
                <button 
                  className="mark-read-button"
                  onClick={() => handleMarkAsRead(message.id)}
                >
                  Mark as read
                </button>
              )}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default NotificationsUI;
