import React, { useState, useEffect } from 'react';
import ControlPanel from './components/ControlPanel';
import NotificationsUI from './components/NotificationsUI';
import MasterCheck from './components/MasterCheck';
import BroadcastListener from './components/BroadcastListener';
import { AuthState, SSEMessage } from './types';
import './App.css';

const App: React.FC = () => {
  // Mock auth data - same as in index.html
  const [auth] = useState<AuthState>({
    token: 'mock-jwt-token',
    userId: 'user_123',
    tenantId: 'tenant_123',
    role: 'admin'
  });

  const [tabStatus, setTabStatus] = useState<string>('Checking tab status...');
  const [refreshTrigger, setRefreshTrigger] = useState<number>(0);

  // Check master tab status
  useEffect(() => {
    const updateTabStatus = () => {
      const currentMaster = localStorage.getItem('sse-master-tab');
      if (currentMaster) {
        setTabStatus('Master Tab (SSE Connected)');
      } else {
        setTabStatus('Slave Tab (Listening to Broadcast)');
      }
    };

    // Initial check
    updateTabStatus();

    // Listen for storage changes (for tab status)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'sse-master-tab') {
        updateTabStatus();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Check periodically
    const interval = setInterval(updateTabStatus, 1000);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(interval);
    };
  }, []);

  // Handle new messages from broadcast channel
  const handleNewMessage = (message: SSEMessage) => {
    console.log('Received broadcast message:', message);
    // Trigger a refresh of the notifications UI
    setRefreshTrigger(prev => prev + 1);
  };

  // Handle new messages from control panel
  const handleControlPanelMessage = (message: SSEMessage) => {
    console.log('Generated message:', message);
    // Trigger a refresh of the notifications UI
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <div className="app-container">
      <header className="app-header">
        <h1>SSE Broadcast System</h1>
        <div className="tab-status">{tabStatus}</div>
      </header>

      <main className="app-content">
        <MasterCheck auth={auth}>
          <BroadcastListener onMessage={handleNewMessage} />
          
          <ControlPanel 
            auth={auth} 
            onNewMessage={handleControlPanelMessage}
          />

          <NotificationsUI refreshTrigger={refreshTrigger} />
        </MasterCheck>
      </main>
    </div>
  );
};

export default App;
