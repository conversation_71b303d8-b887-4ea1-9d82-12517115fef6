import StorageService from './StorageService';
import { SSEMessage } from '../types';

class SSEClient {
  private eventSource: EventSource | null = null;
  private broadcastChannel: BroadcastChannel | null = null;
  private token: string | null = null;
  private userId: string | null = null;
  private tenantId: string | null = null;
  private role: string | null = null;
  private messageListeners: ((message: SSEMessage) => void)[] = [];
  private reconnectTimer: number | null = null;
  private tabId: string;

  constructor() {
    this.tabId = Date.now().toString() + Math.random().toString(36).substring(2);
    this.initBroadcastChannel();

    // Set up cleanup on page unload
    window.addEventListener('beforeunload', () => {
      this.disconnect();
    });
  }

  private initBroadcastChannel() {
    try {
      this.broadcastChannel = new BroadcastChannel('notifications');
      
      this.broadcastChannel.onmessage = async (event) => {
        const message = event.data;

        // Save message to IndexedDB (for slave tabs)
        // The message should already have an ID from the master tab
        if (message.id) {
          try {
            await StorageService.saveCompleteMessage(message);
          } catch (error) {
            console.error('Error saving broadcast message to IndexedDB:', error);
          }
        }

        // Notify listeners
        this.notifyListeners(message);
      };
    } catch (error) {
      console.error('Error initializing BroadcastChannel:', error);
    }
  }

  async connect(token: string, userId: string, tenantId: string, role: string): Promise<boolean> {
    this.token = token;
    this.userId = userId;
    this.tenantId = tenantId;
    this.role = role;

    // Check if we should be the master tab
    const isMaster = await this.claimMasterRole();
    
    if (isMaster) {
      this.connectToSSE();
    }
    
    return isMaster;
  }

  disconnect() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    
    if (this.broadcastChannel) {
      this.broadcastChannel.close();
      this.broadcastChannel = null;
    }
    
    if (this.reconnectTimer) {
      window.clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    this.releaseMasterRole();
  }

  addMessageListener(listener: (message: SSEMessage) => void) {
    this.messageListeners.push(listener);
  }

  removeMessageListener(listener: (message: SSEMessage) => void) {
    this.messageListeners = this.messageListeners.filter(l => l !== listener);
  }

  private notifyListeners(message: SSEMessage) {
    this.messageListeners.forEach(listener => listener(message));
  }

  private connectToSSE() {
    if (!this.token) return;
    
    // Close existing connection if any
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    
    try {
      // Create a new EventSource connection
      this.eventSource = new EventSource('http://localhost:3000/events', {
        withCredentials: true,
      });
      
      // Add authorization header using fetch
      fetch('http://localhost:3000/events', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.token}`,
        },
        credentials: 'include',
      }).catch(error => {
        console.error('Error connecting to SSE:', error);
      });
      
      this.eventSource.onopen = () => {
        console.log('SSE connection opened');
      };
      
      this.eventSource.onmessage = async (event) => {
        try {
          const data = JSON.parse(event.data);
          
          // Create message object
          const message: Omit<SSEMessage, 'id'> = {
            tenantId: this.tenantId!,
            userId: this.userId!,
            role: this.role!,
            timestamp: Date.now(),
            payload: data,
          };
          
          // Save to IndexedDB
          const savedMessage = await StorageService.saveMessage(message);
          
          // Broadcast to other tabs
          if (this.broadcastChannel) {
            this.broadcastChannel.postMessage(savedMessage);
          }
          
          // Notify listeners
          this.notifyListeners(savedMessage);
        } catch (error) {
          console.error('Error processing SSE message:', error);
        }
      };
      
      this.eventSource.onerror = (error) => {
        console.error('SSE connection error:', error);
        
        // Close the connection
        if (this.eventSource) {
          this.eventSource.close();
          this.eventSource = null;
        }
        
        // Try to reconnect after a delay
        if (this.isMasterTab() && !this.reconnectTimer) {
          this.reconnectTimer = window.setTimeout(() => {
            this.reconnectTimer = null;
            this.connectToSSE();
          }, 5000);
        }
      };
    } catch (error) {
      console.error('Error setting up SSE connection:', error);
    }
  }

  private async claimMasterRole(): Promise<boolean> {
    const currentMaster = localStorage.getItem('sse-master-tab');

    if (!currentMaster) {
      localStorage.setItem('sse-master-tab', this.tabId);
      return true;
    }

    return false;
  }

  private releaseMasterRole() {
    if (this.isMasterTab()) {
      localStorage.removeItem('sse-master-tab');
    }
  }

  private isMasterTab(): boolean {
    const currentMaster = localStorage.getItem('sse-master-tab');
    return currentMaster === this.tabId;
  }
}

// Export as singleton
export default new SSEClient();
