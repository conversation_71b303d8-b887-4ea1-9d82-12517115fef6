import { openDB, IDBPDatabase } from 'idb';
import { SSEMessage } from '../types';
import { v4 as uuidv4 } from 'uuid';

class StorageService {
  private dbPromise: Promise<IDBPDatabase>;
  private readonly DB_NAME = 'NotificationsDB';
  private readonly STORE_NAME = 'messages';
  private readonly MAX_MESSAGES = 100;

  constructor() {
    this.dbPromise = this.initDB();
  }

  private async initDB(): Promise<IDBPDatabase> {
    return openDB(this.DB_NAME, 1, {
      upgrade(db) {
        if (!db.objectStoreNames.contains('messages')) {
          const store = db.createObjectStore('messages', { keyPath: 'id' });
          store.createIndex('timestamp', 'timestamp');
        }
      },
    });
  }

  async saveMessage(message: Omit<SSEMessage, 'id'>): Promise<SSEMessage> {
    const db = await this.dbPromise;
    const newMessage: SSEMessage = {
      ...message,
      id: uuidv4(),
    };

    await db.put(this.STORE_NAME, newMessage);
    await this.pruneOldMessages();

    return newMessage;
  }

  async saveCompleteMessage(message: SSEMessage): Promise<void> {
    const db = await this.dbPromise;
    await db.put(this.STORE_NAME, message);
    await this.pruneOldMessages();
  }

  async getAllMessages(): Promise<SSEMessage[]> {
    const db = await this.dbPromise;
    return db.getAllFromIndex(this.STORE_NAME, 'timestamp');
  }

  async markMessageAsRead(id: string): Promise<void> {
    const db = await this.dbPromise;
    const message = await db.get(this.STORE_NAME, id);
    
    if (message) {
      message.payload.read = true;
      await db.put(this.STORE_NAME, message);
    }
  }

  async clearAllMessages(): Promise<void> {
    const db = await this.dbPromise;
    await db.clear(this.STORE_NAME);
  }

  private async pruneOldMessages(): Promise<void> {
    const db = await this.dbPromise;
    const messages = await this.getAllMessages();
    
    if (messages.length > this.MAX_MESSAGES) {
      // Sort by timestamp (newest first)
      messages.sort((a, b) => b.timestamp - a.timestamp);
      
      // Delete oldest messages beyond the limit
      const messagesToDelete = messages.slice(this.MAX_MESSAGES);
      
      for (const message of messagesToDelete) {
        await db.delete(this.STORE_NAME, message.id);
      }
    }
  }
}

// Export as singleton
export default new StorageService();
