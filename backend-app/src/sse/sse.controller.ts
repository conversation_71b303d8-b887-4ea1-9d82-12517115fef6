import { Controller, Get, Post, Req, Res, Body, Headers, UnauthorizedException } from '@nestjs/common';
import { Response, Request } from 'express';
import { SseService } from './sse.service';
import { AuthService } from '../auth/auth.service';

interface SendEventDto {
  tenantId: string;
  target: 'user' | 'role' | 'tenant';
  targetId: string | null;
  message: string;
}

@Controller()
export class SseController {
  constructor(
    private readonly sseService: SseService,
    private readonly authService: AuthService,
  ) {
    this.sseService.startHeartbeat();
  }

  @Get('events')
  async events(@Req() req: Request, @Res() res: Response, @Headers('authorization') authHeader: string): Promise<void> {
    console.log('SSE connection request received');
    console.log('Authorization header:', authHeader);

    let userId = 'user_123';
    let tenantId = 'tenant_123';
    let role = 'admin';

    // Try to extract token if available
    if (authHeader) {
      const token = this.authService.extractTokenFromHeader(authHeader);
      if (token) {
        const payload = this.authService.verifyToken(token);
        if (payload) {
          userId = payload.userId;
          tenantId = payload.tenantId;
          role = payload.role;
        }
      }
    }

    console.log(`Using credentials: userId=${userId}, tenantId=${tenantId}, role=${role}`);

    // Set headers for SSE
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('X-Accel-Buffering', 'no'); // For NGINX proxy buffering

    // Send initial connection message
    res.write(`data: ${JSON.stringify({ type: 'connection', message: 'Connected to SSE' })}\n\n`);

    // Add client to SSE service
    this.sseService.addClient(tenantId, userId, role, res);
  }

  @Post('send')
  async sendEvent(@Body() eventDto: SendEventDto): Promise<{ success: boolean }> {
    const { tenantId, target, targetId, message } = eventDto;
    const messageData = { type: 'message', content: message, timestamp: new Date().toISOString() };

    switch (target) {
      case 'tenant':
        this.sseService.sendEventToTenant(tenantId, messageData);
        break;
      case 'user':
        if (targetId) {
          this.sseService.sendEventToUser(tenantId, targetId, messageData);
        }
        break;
      case 'role':
        if (targetId) {
          this.sseService.sendEventToRole(tenantId, targetId, messageData);
        }
        break;
    }

    return { success: true };
  }
}
