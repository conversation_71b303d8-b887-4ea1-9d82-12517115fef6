import { Injectable, Logger } from '@nestjs/common';
import { Response } from 'express';

interface SseClient {
  response: Response;
  userId: string;
  tenantId: string;
  role: string;
}

@Injectable()
export class SseService {
  private readonly logger = new Logger(SseService.name);
  private clients: Map<string, Map<string, SseClient>> = new Map();

  addClient(tenantId: string, userId: string, role: string, response: Response): void {
    if (!this.clients.has(tenantId)) {
      this.clients.set(tenantId, new Map());
    }

    const tenantClients = this.clients.get(tenantId);
    if (tenantClients) {
      tenantClients.set(userId, { response, userId, tenantId, role });

      this.logger.log(`Client connected: tenant=${tenantId}, user=${userId}, role=${role}`);

      response.on('close', () => {
        this.removeClient(tenantId, userId);
      });
    }
  }

  removeClient(tenantId: string, userId: string): void {
    const tenantClients = this.clients.get(tenantId);
    if (tenantClients && tenantClients.has(userId)) {
      tenantClients.delete(userId);
      this.logger.log(`Client disconnected: tenant=${tenantId}, user=${userId}`);

      // Clean up empty tenant maps
      if (tenantClients.size === 0) {
        this.clients.delete(tenantId);
      }
    }
  }

  sendEventToClient(client: SseClient, message: any): void {
    const messageString = typeof message === 'string' ? message : JSON.stringify(message);
    client.response.write(`data: ${messageString}\n\n`);
  }

  sendHeartbeat(client: SseClient): void {
    client.response.write(`:heartbeat\n\n`);
  }

  sendEventToTenant(tenantId: string, message: any): void {
    const tenantClients = this.clients.get(tenantId);
    if (!tenantClients) return;

    for (const client of tenantClients.values()) {
      this.sendEventToClient(client, message);
    }
  }

  sendEventToUser(tenantId: string, userId: string, message: any): void {
    const tenantClients = this.clients.get(tenantId);
    if (!tenantClients) return;

    const client = tenantClients.get(userId);
    if (client) {
      this.sendEventToClient(client, message);
    }
  }

  sendEventToRole(tenantId: string, role: string, message: any): void {
    const tenantClients = this.clients.get(tenantId);
    if (!tenantClients) return;

    for (const client of tenantClients.values()) {
      if (client.role === role) {
        this.sendEventToClient(client, message);
      }
    }
  }

  startHeartbeat(): void {
    setInterval(() => {
      for (const tenantClients of this.clients.values()) {
        for (const client of tenantClients.values()) {
          this.sendHeartbeat(client);
        }
      }
    }, 30000); // Send heartbeat every 30 seconds
  }
}
