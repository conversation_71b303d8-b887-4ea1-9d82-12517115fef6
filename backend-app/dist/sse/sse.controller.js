"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SseController = void 0;
const common_1 = require("@nestjs/common");
const sse_service_1 = require("./sse.service");
const auth_service_1 = require("../auth/auth.service");
let SseController = class SseController {
    sseService;
    authService;
    constructor(sseService, authService) {
        this.sseService = sseService;
        this.authService = authService;
        this.sseService.startHeartbeat();
    }
    async events(req, res, authHeader) {
        console.log('SSE connection request received');
        console.log('Authorization header:', authHeader);
        let userId = 'user_123';
        let tenantId = 'tenant_123';
        let role = 'admin';
        if (authHeader) {
            const token = this.authService.extractTokenFromHeader(authHeader);
            if (token) {
                const payload = this.authService.verifyToken(token);
                if (payload) {
                    userId = payload.userId;
                    tenantId = payload.tenantId;
                    role = payload.role;
                }
            }
        }
        console.log(`Using credentials: userId=${userId}, tenantId=${tenantId}, role=${role}`);
        res.setHeader('Content-Type', 'text/event-stream');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');
        res.setHeader('X-Accel-Buffering', 'no');
        res.write(`data: ${JSON.stringify({ type: 'connection', message: 'Connected to SSE' })}\n\n`);
        this.sseService.addClient(tenantId, userId, role, res);
    }
    async sendEvent(eventDto) {
        const { tenantId, target, targetId, message } = eventDto;
        const messageJson = JSON.stringify({ type: 'message', content: message, timestamp: new Date().toISOString() });
        switch (target) {
            case 'tenant':
                this.sseService.sendEventToTenant(tenantId, messageJson);
                break;
            case 'user':
                if (targetId) {
                    this.sseService.sendEventToUser(tenantId, targetId, messageJson);
                }
                break;
            case 'role':
                if (targetId) {
                    this.sseService.sendEventToRole(tenantId, targetId, messageJson);
                }
                break;
        }
        return { success: true };
    }
};
exports.SseController = SseController;
__decorate([
    (0, common_1.Get)('events'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __param(2, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, String]),
    __metadata("design:returntype", Promise)
], SseController.prototype, "events", null);
__decorate([
    (0, common_1.Post)('send'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SseController.prototype, "sendEvent", null);
exports.SseController = SseController = __decorate([
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [sse_service_1.SseService,
        auth_service_1.AuthService])
], SseController);
//# sourceMappingURL=sse.controller.js.map