import { Response, Request } from 'express';
import { SseService } from './sse.service';
import { AuthService } from '../auth/auth.service';
interface SendEventDto {
    tenantId: string;
    target: 'user' | 'role' | 'tenant';
    targetId: string | null;
    message: string;
}
export declare class SseController {
    private readonly sseService;
    private readonly authService;
    constructor(sseService: SseService, authService: AuthService);
    events(req: Request, res: Response, authHeader: string): Promise<void>;
    sendEvent(eventDto: SendEventDto): Promise<{
        success: boolean;
    }>;
}
export {};
