import { Response } from 'express';
interface SseClient {
    response: Response;
    userId: string;
    tenantId: string;
    role: string;
}
export declare class SseService {
    private readonly logger;
    private clients;
    addClient(tenantId: string, userId: string, role: string, response: Response): void;
    removeClient(tenantId: string, userId: string): void;
    sendEventToClient(client: SseClient, message: string): void;
    sendHeartbeat(client: SseClient): void;
    sendEventToTenant(tenantId: string, message: string): void;
    sendEventToUser(tenantId: string, userId: string, message: string): void;
    sendEventToRole(tenantId: string, role: string, message: string): void;
    startHeartbeat(): void;
}
export {};
