import { Response } from 'express';
interface SseClient {
    response: Response;
    userId: string;
    tenantId: string;
    role: string;
}
export declare class SseService {
    private readonly logger;
    private clients;
    addClient(tenantId: string, userId: string, role: string, response: Response): void;
    removeClient(tenantId: string, userId: string): void;
    sendEventToClient(client: SseClient, message: any): void;
    sendHeartbeat(client: SseClient): void;
    sendEventToTenant(tenantId: string, message: any): void;
    sendEventToUser(tenantId: string, userId: string, message: any): void;
    sendEventToRole(tenantId: string, role: string, message: any): void;
    startHeartbeat(): void;
}
export {};
