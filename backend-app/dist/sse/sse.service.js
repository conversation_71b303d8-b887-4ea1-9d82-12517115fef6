"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var SseService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SseService = void 0;
const common_1 = require("@nestjs/common");
let SseService = SseService_1 = class SseService {
    logger = new common_1.Logger(SseService_1.name);
    clients = new Map();
    addClient(tenantId, userId, role, response) {
        if (!this.clients.has(tenantId)) {
            this.clients.set(tenantId, new Map());
        }
        const tenantClients = this.clients.get(tenantId);
        if (tenantClients) {
            tenantClients.set(userId, { response, userId, tenantId, role });
            this.logger.log(`Client connected: tenant=${tenantId}, user=${userId}, role=${role}`);
            response.on('close', () => {
                this.removeClient(tenantId, userId);
            });
        }
    }
    removeClient(tenantId, userId) {
        const tenantClients = this.clients.get(tenantId);
        if (tenantClients && tenantClients.has(userId)) {
            tenantClients.delete(userId);
            this.logger.log(`Client disconnected: tenant=${tenantId}, user=${userId}`);
            if (tenantClients.size === 0) {
                this.clients.delete(tenantId);
            }
        }
    }
    sendEventToClient(client, message) {
        client.response.write(`data: ${message}\n\n`);
    }
    sendHeartbeat(client) {
        client.response.write(`:heartbeat\n\n`);
    }
    sendEventToTenant(tenantId, message) {
        const tenantClients = this.clients.get(tenantId);
        if (!tenantClients)
            return;
        for (const client of tenantClients.values()) {
            this.sendEventToClient(client, message);
        }
    }
    sendEventToUser(tenantId, userId, message) {
        const tenantClients = this.clients.get(tenantId);
        if (!tenantClients)
            return;
        const client = tenantClients.get(userId);
        if (client) {
            this.sendEventToClient(client, message);
        }
    }
    sendEventToRole(tenantId, role, message) {
        const tenantClients = this.clients.get(tenantId);
        if (!tenantClients)
            return;
        for (const client of tenantClients.values()) {
            if (client.role === role) {
                this.sendEventToClient(client, message);
            }
        }
    }
    startHeartbeat() {
        setInterval(() => {
            for (const tenantClients of this.clients.values()) {
                for (const client of tenantClients.values()) {
                    this.sendHeartbeat(client);
                }
            }
        }, 30000);
    }
};
exports.SseService = SseService;
exports.SseService = SseService = SseService_1 = __decorate([
    (0, common_1.Injectable)()
], SseService);
//# sourceMappingURL=sse.service.js.map