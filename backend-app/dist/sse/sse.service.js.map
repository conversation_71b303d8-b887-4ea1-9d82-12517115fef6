{"version": 3, "file": "sse.service.js", "sourceRoot": "", "sources": ["../../src/sse/sse.service.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAAoD;AAW7C,IAAM,UAAU,kBAAhB,MAAM,UAAU;IACJ,MAAM,GAAG,IAAI,eAAM,CAAC,YAAU,CAAC,IAAI,CAAC,CAAC;IAC9C,OAAO,GAAwC,IAAI,GAAG,EAAE,CAAC;IAEjE,SAAS,CAAC,QAAgB,EAAE,MAAc,EAAE,IAAY,EAAE,QAAkB;QAC1E,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QACxC,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,aAAa,EAAE,CAAC;YAClB,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YAEhE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,QAAQ,UAAU,MAAM,UAAU,IAAI,EAAE,CAAC,CAAC;YAEtF,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;gBACxB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,YAAY,CAAC,QAAgB,EAAE,MAAc;QAC3C,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,aAAa,IAAI,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/C,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,QAAQ,UAAU,MAAM,EAAE,CAAC,CAAC;YAG3E,IAAI,aAAa,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBAC7B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;IACH,CAAC;IAED,iBAAiB,CAAC,MAAiB,EAAE,OAAe;QAClD,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,OAAO,MAAM,CAAC,CAAC;IAChD,CAAC;IAED,aAAa,CAAC,MAAiB;QAC7B,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IAC1C,CAAC;IAED,iBAAiB,CAAC,QAAgB,EAAE,OAAe;QACjD,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,aAAa;YAAE,OAAO;QAE3B,KAAK,MAAM,MAAM,IAAI,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC;YAC5C,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,eAAe,CAAC,QAAgB,EAAE,MAAc,EAAE,OAAe;QAC/D,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,aAAa;YAAE,OAAO;QAE3B,MAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,eAAe,CAAC,QAAgB,EAAE,IAAY,EAAE,OAAe;QAC7D,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,aAAa;YAAE,OAAO;QAE3B,KAAK,MAAM,MAAM,IAAI,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC;YAC5C,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBACzB,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;IACH,CAAC;IAED,cAAc;QACZ,WAAW,CAAC,GAAG,EAAE;YACf,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;gBAClD,KAAK,MAAM,MAAM,IAAI,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC;oBAC5C,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC;QACH,CAAC,EAAE,KAAK,CAAC,CAAC;IACZ,CAAC;CACF,CAAA;AAjFY,gCAAU;qBAAV,UAAU;IADtB,IAAA,mBAAU,GAAE;GACA,UAAU,CAiFtB"}