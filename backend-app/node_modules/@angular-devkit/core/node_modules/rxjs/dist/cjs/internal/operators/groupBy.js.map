{"version": 3, "file": "groupBy.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/groupBy.ts"], "names": [], "mappings": ";;;AAAA,4CAA2C;AAC3C,qDAAoD;AACpD,sCAAqC;AAErC,qCAAuC;AACvC,2DAAoF;AAuIpF,SAAgB,OAAO,CACrB,WAA4B,EAC5B,gBAAgH,EAChH,QAAyE,EACzE,SAAkC;IAElC,OAAO,cAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAChC,IAAI,OAAqC,CAAC;QAC1C,IAAI,CAAC,gBAAgB,IAAI,OAAO,gBAAgB,KAAK,UAAU,EAAE;YAC/D,OAAO,GAAG,gBAAyC,CAAC;SACrD;aAAM;YACL,CAAG,QAAQ,GAAyB,gBAAgB,SAAzC,EAAE,OAAO,GAAgB,gBAAgB,QAAhC,EAAE,SAAS,GAAK,gBAAgB,UAArB,CAAsB,CAAC;SACvD;QAGD,IAAM,MAAM,GAAG,IAAI,GAAG,EAAuB,CAAC;QAG9C,IAAM,MAAM,GAAG,UAAC,EAAkC;YAChD,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACnB,EAAE,CAAC,UAAU,CAAC,CAAC;QACjB,CAAC,CAAC;QAIF,IAAM,WAAW,GAAG,UAAC,GAAQ,IAAK,OAAA,MAAM,CAAC,UAAC,QAAQ,IAAK,OAAA,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,EAAnB,CAAmB,CAAC,EAAzC,CAAyC,CAAC;QAG5E,IAAI,YAAY,GAAG,CAAC,CAAC;QAGrB,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAS9B,IAAM,uBAAuB,GAAG,IAAI,uCAAkB,CACpD,UAAU,EACV,UAAC,KAAQ;YAIP,IAAI;gBACF,IAAM,KAAG,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;gBAE/B,IAAI,OAAK,GAAG,MAAM,CAAC,GAAG,CAAC,KAAG,CAAC,CAAC;gBAC5B,IAAI,CAAC,OAAK,EAAE;oBAEV,MAAM,CAAC,GAAG,CAAC,KAAG,EAAE,CAAC,OAAK,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,iBAAO,EAAO,CAAC,CAAC,CAAC;oBAKxE,IAAM,OAAO,GAAG,uBAAuB,CAAC,KAAG,EAAE,OAAK,CAAC,CAAC;oBACpD,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAEzB,IAAI,QAAQ,EAAE;wBACZ,IAAM,oBAAkB,GAAG,6CAAwB,CAMjD,OAAY,EACZ;4BAGE,OAAM,CAAC,QAAQ,EAAE,CAAC;4BAClB,oBAAkB,aAAlB,oBAAkB,uBAAlB,oBAAkB,CAAE,WAAW,EAAE,CAAC;wBACpC,CAAC,EAED,SAAS,EAGT,SAAS,EAET,cAAM,OAAA,MAAM,CAAC,MAAM,CAAC,KAAG,CAAC,EAAlB,CAAkB,CACzB,CAAC;wBAGF,uBAAuB,CAAC,GAAG,CAAC,qBAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,oBAAkB,CAAC,CAAC,CAAC;qBACzF;iBACF;gBAGD,OAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;aAC9C;YAAC,OAAO,GAAG,EAAE;gBACZ,WAAW,CAAC,GAAG,CAAC,CAAC;aAClB;QACH,CAAC,EAED,cAAM,OAAA,MAAM,CAAC,UAAC,QAAQ,IAAK,OAAA,QAAQ,CAAC,QAAQ,EAAE,EAAnB,CAAmB,CAAC,EAAzC,CAAyC,EAE/C,WAAW,EAKX,cAAM,OAAA,MAAM,CAAC,KAAK,EAAE,EAAd,CAAc,EACpB;YACE,iBAAiB,GAAG,IAAI,CAAC;YAIzB,OAAO,YAAY,KAAK,CAAC,CAAC;QAC5B,CAAC,CACF,CAAC;QAGF,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;QAO1C,SAAS,uBAAuB,CAAC,GAAM,EAAE,YAA8B;YACrE,IAAM,MAAM,GAAQ,IAAI,uBAAU,CAAI,UAAC,eAAe;gBACpD,YAAY,EAAE,CAAC;gBACf,IAAM,QAAQ,GAAG,YAAY,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;gBACzD,OAAO;oBACL,QAAQ,CAAC,WAAW,EAAE,CAAC;oBAIvB,EAAE,YAAY,KAAK,CAAC,IAAI,iBAAiB,IAAI,uBAAuB,CAAC,WAAW,EAAE,CAAC;gBACrF,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;YACjB,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAxID,0BAwIC"}